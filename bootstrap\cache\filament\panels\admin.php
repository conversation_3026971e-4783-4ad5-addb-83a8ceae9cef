<?php return array (
  'livewireComponents' => 
  array (
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.widgets.account-widget' => 'Filament\\Widgets\\AccountWidget',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'app.filament.admin.auth.custom-login' => 'App\\Filament\\Admin\\Auth\\CustomLogin',
    'diogo-g-pinto.auth-u-i-enhancer.pages.auth.password-reset.auth-ui-enhancer-request-password-reset' => 'DiogoGPinto\\AuthUIEnhancer\\Pages\\Auth\\PasswordReset\\AuthUiEnhancerRequestPasswordReset',
    'diogo-g-pinto.auth-u-i-enhancer.pages.auth.password-reset.auth-ui-enhancer-reset-password' => 'DiogoGPinto\\AuthUIEnhancer\\Pages\\Auth\\PasswordReset\\AuthUiEnhancerResetPassword',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.list-roles' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\ListRoles',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.create-role' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\CreateRole',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.view-role' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\ViewRole',
    'bezhan-salleh.filament-shield.resources.role-resource.pages.edit-role' => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource\\Pages\\EditRole',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'Filament\\Pages\\Dashboard',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Pages',
  ),
  'resources' => 
  array (
    0 => 'BezhanSalleh\\FilamentShield\\Resources\\RoleResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Resources',
  ),
  'widgets' => 
  array (
    0 => 'Filament\\Widgets\\AccountWidget',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\new-addrus\\app\\Filament/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Widgets',
  ),
);